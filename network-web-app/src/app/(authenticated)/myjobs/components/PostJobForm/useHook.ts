import {
  BasicJobDataPayloadI,
  JobBenefitsPayloadI,
  JobFormDataI,
  JobRequirementsPayloadI,
} from './types';
import showToast from '@/utils/toast';
import { useForm } from 'react-hook-form';
import { useState, useEffect } from 'react';
import {
  createJobPost,
  updateBenefits,
  updateRequirements,
} from '@/networks/jobs/submitJob';
import { useRouter } from 'next/navigation';
import { useProfileSession } from '@/context/ProfileSessionContext';
import { fetchEntityProfilesForUserAPI } from '@/networks/entityProfile';

const defaultValues: JobFormDataI = {
  entityId: '',
  designationId: '',
  departmentId: '',
  shipImo: '',
  jobType: '',
  locationCountryIso2: '',
  genderDiversityIndex: 0.0,
  expiryDate: '',
  isOfficial: false,
  minYears: 0,
  minSalary: 0,
  shipType: undefined,
  about: '',
  roles: '',
  benefits: '',
  certifications: [
    {
      certification: '',
      mandatory: false,
    },
  ],
  documents: [
    {
      documentType: '',
      countries: [],
      mandatory: false,
    },
  ],
  experiences: [
    {
      designation: '',
      shipType: '',
      months: 0,
      mandatory: false,
    },
  ],
  equipments: [
    {
      type: '',
      make: '',
      model: '',
      fuelType: false,
      moe: 0,
      mandatory: false,
    },
  ],
  cargos: [
    {
      name: '',
      code: '',
      months: 0,
      mandatory: false,
    },
  ],
  skills: [
    {
      name: '',
      mandatory: false,
    },
  ],
  otherRequirements: [
    {
      no: 1,
      details: '',
      mandatory: false,
    },
  ],
  salaryType: 'CONTRACTUAL',
  salaryCurrency: 'USD',
  salaryMin: 0,
  salaryMax: 0,
  salaryMandatory: true,
  contractUnit: 'months',
  contractDuration: 0,
  internetAvailability: 'NO',
  internetSpeed: 0,
  internetLimitPerDay: 0,
  internetDetails: '',
  insuranceType: 'SELF',
  itfType: 'ITF',
  familyOnboard: 'NO',
  benefitItems: [
    {
      no: 1,
      details: '',
    },
  ],
};

const applicationTypesOptions = [
  { value: 'normal', label: 'Normal' },
  { value: 'link', label: 'Link' },
  { value: 'email', label: 'Email' },
];

const usePostJobForm = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmittingRequirements, setIsSubmittingRequirements] =
    useState(false);
  const [isSubmittingBenefits, setIsSubmittingBenefits] = useState(false);
  const [step, setStep] = useState<number>(1);
  const { activeProfileType, entityProfileId } = useProfileSession();
  const methods = useForm<JobFormDataI>({
    defaultValues,
    mode: 'onChange',
  });
  const router = useRouter();

  useEffect(() => {
    const prefillCompanyField = async () => {
      if (activeProfileType === 'ENTITY' && entityProfileId) {
        try {
          const entities = await fetchEntityProfilesForUserAPI();
          const currentEntity = entities.find(e => e.id === entityProfileId);

          if (currentEntity) {
            methods.setValue('entityId', currentEntity.entity.id);
            methods.setValue('entity', currentEntity.entity);
          }
        } catch (error) {
          console.error('Failed to fetch entity profiles for prefill:', error);
        }
      }
    };
    prefillCompanyField();
  }, [activeProfileType, entityProfileId, methods]);

  const handleBack = () => {
    switch (step) {
      case 2:
        setStep(1);
        break;
      case 3:
        setStep(2);
      default:
    }
  };

  const onSubmit = async (data: JobFormDataI) => {
    try {
      setIsSubmitting(true);
      if (data.applicationType === 'link' && !data.applicationLink) {
        showToast({ type: 'error', message: 'Application Link is required' });
        return;
      }
      if (data.applicationType === 'email' && !data.applicationEmail) {
        showToast({ type: 'error', message: 'Application Email is required' });
        return;
      }

      const payload: BasicJobDataPayloadI = {
        entity: data.entity!,
        designation: data.designation!,
        department: data.department!,
        expiryDate: new Date(data.expiryDate),
        isOfficial: activeProfileType === 'ENTITY' ? true : false,
        minYears: data.minYears,
        minSalary: data.minSalary,
      };

      if (data.jobType)
        payload.jobType = data.jobType as 'SAILING' | 'NON_SAILING';
      if (data.locationCountryIso2)
        payload.countryIso2 = data.locationCountryIso2;
      if (typeof data.genderDiversityIndex === 'number')
        payload.genderDiversityIndex = data.genderDiversityIndex;
      if (typeof data.maxYears === 'number') payload.maxYears = data.maxYears;
      if (typeof data.maxSalary === 'number')
        payload.maxSalary = data.maxSalary;

      if (data.ship && data.ship.imo && data.ship.dataType)
        payload.ship = data.ship;

      if (data.applicationType === 'link')
        payload.applicationMethod = 'EXTERNAL_LINK';
      else if (data.applicationType === 'email')
        payload.applicationMethod = 'EMAIL';
      else payload.applicationMethod = 'IN_APP';

      if (data.applicationLink) payload.applicationUrl = data.applicationLink;
      if (data.applicationEmail)
        payload.applicationEmail = data.applicationEmail;

      console.log('Submitting Stage 1 payload', payload);
      const result = await createJobPost(payload);

      const draftId = (result as { id?: string } | null)?.id;
      if (draftId) {
        methods.setValue('draftJobId', draftId);
      }

      showToast({
        type: 'success',
        message: 'Step 1 saved',
        description: 'Proceeding to step 2...',
      });
      setStep(2);
    } catch (e) {
      console.error(e);
      showToast({ type: 'error', message: 'Error submitting form' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const submitRequirements = async (isAdvanced: boolean = false) => {
    const draftId = methods.getValues('draftJobId');
    if (!draftId) {
      showToast({ type: 'error', message: 'Draft job not created yet' });
      return;
    }
    try {
      setIsSubmittingRequirements(true);
      const data = methods.getValues();

      const payload: JobRequirementsPayloadI = {
        stage: '2',
        about: data.about || undefined,
        rolesResponsibilities: data.roles || undefined,
        requirementType: isAdvanced ? 'ADVANCED' : 'BASIC',
        benefits: data.benefits || undefined,
      };

      // Add advanced requirements if in advanced mode
      if (isAdvanced) {
        // Certification requirements
        if (data.certifications?.length) {
          payload.certificationRequirements = data.certifications
            .filter(cert => cert.certification)
            .map(cert => ({
              certification: {
                id: cert.certification!,
                dataType: 'master' as const,
              },
              isMandatory: cert.mandatory || false,
            }));
        }

        // Document requirements
        if (data.documents?.length) {
          payload.documentRequirements = data.documents
            .filter(doc => doc.documentType)
            .map(doc => ({
              documentType: {
                id: doc.documentType!,
                dataType: 'master' as const,
              },
              countries: doc.countries || [],
              isMandatory: doc.mandatory || false,
            }));
        }

        // Experience requirements
        if (data.experiences?.length) {
          payload.experienceRequirements = data.experiences
            .filter(exp => exp.designation && exp.months)
            .map(exp => ({
              designation: exp.designation
                ? { id: exp.designation, dataType: 'master' as const }
                : undefined,
              shipType: exp.shipType
                ? { id: exp.shipType, dataType: 'master' as const }
                : undefined,
              monthsOfExperience: Number(exp.months) || 0,
              isMandatory: exp.mandatory || false,
              isTotal: false,
            }));
        }

        // Equipment requirements
        if(data.equipments?.length) {
            
        }

        // Skill requirements
        if (data.skills?.length) {
          payload.skillRequirements = data.skills
            .filter(skill => skill.name)
            .map(skill => ({
              skill: { id: skill.name!, dataType: 'master' as const },
              isMandatory: skill.mandatory || false,
            }));
        }

        // Cargo requirements
        if (data.cargos?.length) {
          payload.cargoRequirements = data.cargos
            .filter(cargo => cargo.name && cargo.months)
            .map(cargo => ({
              name: cargo.name!,
              code: cargo.code || undefined,
              monthsOfExperience: Number(cargo.months) || 0,
              isMandatory: cargo.mandatory || false,
            }));
        }

        // Other requirements
        if (data.otherRequirements?.length) {
          payload.otherRequirements = data.otherRequirements
            .filter(req => req.details?.trim())
            .map(req => ({
              details: req.details!.trim(),
              isMandatory: req.mandatory || false,
            }));
        }
      }

      await updateRequirements({ draftId, payload });
      showToast({ type: 'success', message: 'Requirements saved' });
      setStep(3);
    } catch (e) {
      console.error(e);
      showToast({ type: 'error', message: 'Error saving requirements' });
    } finally {
      setIsSubmittingRequirements(false);
    }
  };

  const submitBenefits = async () => {
    const draftId = methods.getValues('draftJobId');
    if (!draftId) {
      showToast({ type: 'error', message: 'Draft job not created yet' });
      return;
    }
    try {
      setIsSubmittingBenefits(true);
      const data = methods.getValues();

      const internetAvailable = data.internetAvailability
        ? data.internetAvailability === 'YES'
        : undefined;
      const familyOnboard = data.familyOnboard
        ? data.familyOnboard === 'YES'
        : undefined;

      const payload: JobBenefitsPayloadI = {
        stage: '3',
        benefitType:
          (data.benefitItems && data.benefitItems.length > 0) || data.salaryType
            ? 'ADVANCED'
            : 'BASIC',
        benefits: data.benefits || undefined,
        salaryType: data.salaryType || undefined,
        currencyCode: data.salaryCurrency || undefined,
        minSalary: Number(data.salaryMin ?? 0),
        maxSalary: Number(data.salaryMax ?? 0),
        showSalary: data?.salaryMandatory || false,
        contractMonths:
          data.contractUnit === 'months'
            ? Number(data.contractDuration)
            : undefined,
        contractDays:
          data.contractUnit === 'days'
            ? Number(data.contractDuration)
            : undefined,
        internetAvailable,
        internetSpeed: Number(data.internetSpeed) || 0,
        internetLimitPerDay: Number(data.internetLimitPerDay) || 0,
        internetDetails: data.internetDetails,
        insuranceType: data.insuranceType,
        familyOnboard,
        itfType: data.itfType || undefined,
        benefitDetails: (data.benefitItems || [])
          .filter(i => i && i.details && i.details.trim() !== '')
          .map(i => ({ details: i.details!.trim() })),
      };

      await updateBenefits({ draftId, payload });
      showToast({ type: 'success', message: 'Benefits saved' });
      router.push('/jobs');
    } catch (e) {
      console.error(e);
      showToast({ type: 'error', message: 'Error saving benefits' });
    } finally {
      setIsSubmittingBenefits(false);
    }
  };

  return {
    methods,
    onSubmit,
    isSubmitting,
    isSubmittingRequirements,
    step,
    setStep,
    submitRequirements,
    submitBenefits,
    isSubmittingBenefits,
    handleBack,
    applicationTypesOptions,
  };
};

export default usePostJobForm;
