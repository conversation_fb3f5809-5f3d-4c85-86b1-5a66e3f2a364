'use client';

import { useParams } from 'next/navigation';
import React from 'react';
import DashboardLayout from '../../forums/components/DashboardLayout';
import JobDetail from './components/JobDetail';

const JobDetailPage = () => {
  const params = useParams();
  const slug = (params?.slug as string) || '';

  return (
    <DashboardLayout>
      <JobDetail jobId={slug} />
    </DashboardLayout>
  );
};

export default JobDetailPage;
