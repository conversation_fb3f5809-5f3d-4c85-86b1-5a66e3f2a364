export type JobStatusI = 'ACTIVE' | 'DELETED' | 'EXPIRED';

export type IdTypeI = {
  id: string;
  dataType: 'master' | 'raw';
};

export type IdNameTypeI = {
  id: string;
  name: string;
  type: string;
};

export type ShipNestedClientI = {
  imo: string;
  name: string;
  dataType: string;
};

export type FetchJobsQueryI = {
  entity?: IdTypeI;
  isOfficial?: boolean;
  status?: JobStatusI;
  pageSize: number;
  cursorId: string | null;
};

export type FetchJobsResultItemI = {
  id: string;
  cursorId: string;
  isUrgent: boolean;
  entity: IdNameTypeI;
  designation: IdNameTypeI;
  department: IdNameTypeI;
  ship: ShipNestedClientI;
  minYears: number;
  maxYears?: number | null;
  minSalary: number;
  maxSalary?: number | null;
  status?: JobStatusI | null;
  expiryDate?: string | null;
  isOfficial: boolean;
  createdAt: string;
  applicationStatus?: string | null;
  matching?: number | null;
};

export type FetchJobsResponseI = {
  data: FetchJobsResultItemI[];
  nextCursorId: number | null;
};

export type JobCandidateFetchOneResultI = {
  id: string;
  entity?: { id?: string; name?: string } | null;
  designation?: { id?: string; name?: string } | null;
  matching?: number | null;
  createdAt?: string;
  applicationStatus?: string | null;
};

export type ApplicantsStatusI =
  | 'PENDING'
  | 'WITHDREW'
  | 'SHORTLISTED'
  | 'REJECTED_BY_ENTITY'
  | 'OFFERED'
  | 'ACCEPTED_BY_APPLICANT';

export type FetchApplicantsQueryI = {
  jobId: string;
  status?: ApplicantsStatusI;
  cursorId: string | null;
  pageSize: number;
};

export type ApplicantProfileI = {
  id: string;
  name: string;
  avatar: string | null;
  designation?: { id?: string; name?: string } | null;
  entity?: { id?: string; name?: string } | null;
};

export type FetchApplicantsResultItemI = {
  id: string;
  cursorId: string | null;
  matching: number;
  status: ApplicantsStatusI;
  ApplicantProfile: ApplicantProfileI;
  DecisionMakerProfile: ApplicantProfileI;
};

export type FetchApplicantsResponseI = {
  data: FetchApplicantsResultItemI[];
  nextCursorId: number | null;
};

export type FetchJobsForApplicantQueryI = {
  cursorId: string | null;
  pageSize: number;
  status: ApplicantsStatusI;
};

export type FetchJobsForApplicantResultItemI = {
  id: string;
  cursorId: string | null;
  status: ApplicantsStatusI;
  matching: number;
  designation: IdNameTypeI;
  entity: IdNameTypeI;
  createdAt: string;
};

export type FetchJobsForApplicantResponseI = {
  data: FetchJobsForApplicantResultItemI[];
  nextCursorId: number | null;
};
